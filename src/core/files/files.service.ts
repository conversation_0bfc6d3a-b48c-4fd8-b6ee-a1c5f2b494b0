import {
  Injectable,
  Inject,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { CLOUDINARY } from './files.module';
import {
  v2 as cloudinary,
  UploadApiResponse,
  UploadApiErrorResponse,
} from 'cloudinary';
import {
  FileUploadOptions,
  FileUploadResult,
} from 'src/common/interfaces/cloudinary.interface';
import { Readable } from 'stream';

@Injectable()
export class FilesService {
  constructor(
    @Inject(CLOUDINARY) private cloudinaryConfig: typeof cloudinary,
  ) {}

  async uploadFile(
    file: File,
    options: FileUploadOptions = {},
  ): Promise<FileUploadResult> {
    try {
      if (!file) {
        throw new BadRequestException('No file provided');
      }

      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize)
        throw new BadRequestException('File size exceeds 10MB limit');
      const uploadOptions = {
        folder: options.folder || 'uploads',
        resource_type: options.resource_type || 'auto',
        overwrite: options.overwrite ?? false,
        quality: options.quality || 'auto',
        ...options,
      };

      return new Promise<FileUploadResult>((resolve, reject) => {
        const uploadStream = cloudinary.uploader.upload_stream(
          uploadOptions,
          (
            error: UploadApiErrorResponse | undefined,
            result: UploadApiResponse | undefined,
          ) => {
            if (error) {
              reject(
                new InternalServerErrorException(
                  `Upload failed: ${error.message}`,
                ),
              );
            } else if (result) {
              resolve({
                public_id: result.public_id,
                url: result.url,
                secure_url: result.secure_url,
                format: result.format,
                width: result.width,
                height: result.height,
                bytes: result.bytes,
                resource_type: result.resource_type,
                created_at: result.created_at,
                original_filename: result.original_filename,
              });
            }
          },
        );

        // Convert buffer to stream and pipe to Cloudinary
        const bufferStream = new Readable();
        bufferStream.push(file.buffer);
        bufferStream.push(null);
        bufferStream.pipe(uploadStream);
      });
    } catch (error) {
      throw new InternalServerErrorException(
        `File upload failed: ${error.message}`,
      );
    }
  }
}
