import { Injectable, Inject } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { users, User, NewUser } from '../schemas';
import * as schema from '../schemas/users';
import { IUserRepository } from '../interfaces/user-repository.interface';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

@Injectable()
export class UserRepository implements IUserRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}
  async bandUser(id: number): Promise<User | null> {
    const result = await this.db
      .update(users)
      .set({
        isBanned: true,
        bannedAt: new Date(),
      })
      .where(eq(users.id, id))
      .returning();

    return result[0] || null;
  }
  async restoreUser(id: number): Promise<User | null> {
    const result = await this.db
      .update(users)
      .set({
        isBanned: false,
        bannedAt: undefined,
      })
      .where(eq(users.id, id))
      .returning();

    return result[0] || null;
  }
  async softDeleteUser(id: number): Promise<User | null> {
    const result = await this.db
      .update(users)
      .set({
        isDeleted: true,
        deletedAt: new Date(),
      })
      .where(eq(users.id, id))
      .returning();

    return result[0] || null;
  }

  async findAll(limit?: number, offset?: number): Promise<User[]> {
    return this.db
      .select()
      .from(users)
      .offset(offset || 0)
      .limit(limit || 10);
  }

  async findById(id: number): Promise<User | null> {
    const result = await this.db
      .select()
      .from(users)
      .where(eq(users.id, id))
      .limit(1);

    return result[0] || null;
  }

  async findByEmail(email: string): Promise<User | null> {
    const result = await this.db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    return result[0] || null;
  }

  async findActiveUsers(): Promise<User[]> {
    return this.db.select().from(users).where(eq(users.isActive, true));
  }

  async create(userData: NewUser): Promise<User> {
    const result = await this.db
      .insert(users)
      .values({
        ...userData,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return result[0];
  }

  async update(id: number, userData: Partial<User>): Promise<User | null> {
    const result = await this.db
      .update(users)
      .set({
        ...userData,
        updatedAt: new Date(),
      })
      .where(eq(users.id, id))
      .returning();

    return result[0] || null;
  }

  async deactivateUser(id: number): Promise<User | null> {
    return this.update(id, { isActive: false });
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(users)
      .where(eq(users.id, id))
      .returning({ id: users.id });

    return result.length > 0;
  }
}
