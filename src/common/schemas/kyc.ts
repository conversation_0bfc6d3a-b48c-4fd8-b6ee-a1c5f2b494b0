import { pgTable, serial, varchar, timestamp } from 'drizzle-orm/pg-core';
import { pgEnum } from 'drizzle-orm/pg-core';

export const kycStatusEnum = pgEnum('kyc_status', [
  'pending',
  'approved',
  'rejected',
]);
export const kyc = pgTable('kyc', {
  id: serial('id').primaryKey(),
  userId: varchar('user_id', { length: 255 }).unique(),
  status: kycStatusEnum('status').notNull(),
  // documents: varchar('documents', { length: 255 })[],
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});
