import { User } from '../schemas';
import { IRepository } from './repository.interface';

export interface IUserRepository extends IRepository<User> {
  findByEmail(email: string): Promise<User | null>;
  findActiveUsers(): Promise<User[]>;
  deactivateUser(id: number): Promise<User | null>;
  bandUser(id: number): Promise<User | null>;
  restoreUser(id: number): Promise<User | null>;
  softDeleteUser(id: number): Promise<User | null>;
}
